#!/usr/bin/env ts-node

/**
 * Test script to verify avatar upload functionality
 * This script tests the complete avatar upload flow including:
 * - Storage service configuration
 * - Path generation
 * - Supabase storage upload
 * - RLS policy enforcement
 */

import { StorageService } from '../services/r2Storage';

// Create a simple test image blob
function createTestImageBlob(): Blob {
  // Create a simple 1x1 pixel JPEG image as base64
  const base64Data = '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A';
  
  // Convert base64 to binary
  const binaryString = atob(base64Data);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  return new Blob([bytes], { type: 'image/jpeg' });
}

async function testAvatarUpload() {
  console.log('🧪 Testing Avatar Upload Functionality\n');
  
  try {
    // Test 1: Check storage health
    console.log('1️⃣ Checking storage health...');
    const health = await StorageService.checkStorageHealth();
    console.log('Storage health:', health);
    
    if (!health.supabaseAvailable) {
      console.error('❌ Supabase storage is not available. Cannot proceed with test.');
      return;
    }
    
    console.log('✅ Storage is healthy!\n');

    // Test 2: Test path generation
    console.log('2️⃣ Testing path generation...');
    const testUserId = 'test-user-12345';
    const testPath = StorageService.generateImagePath(testUserId, 'avatar', 'jpg');
    console.log('Generated path:', testPath);
    
    // Verify path format
    const pathParts = testPath.split('/');
    if (pathParts.length !== 3 || pathParts[0] !== testUserId || pathParts[1] !== 'avatars') {
      console.error('❌ Path generation failed. Expected format: userId/avatars/filename.jpg');
      return;
    }
    
    console.log('✅ Path generation working correctly!\n');

    // Test 3: Create test image
    console.log('3️⃣ Creating test image...');
    const testImage = createTestImageBlob();
    console.log('Test image created:', { size: testImage.size, type: testImage.type });
    console.log('✅ Test image created!\n');

    // Test 4: Test upload
    console.log('4️⃣ Testing image upload...');
    const uploadResult = await StorageService.uploadImage(
      testImage,
      testPath,
      'image/jpeg'
    );
    
    console.log('Upload result:', uploadResult);
    
    if (uploadResult.success) {
      console.log('✅ Upload successful!');
      console.log('📸 Image URL:', uploadResult.url);
      
      // Test 5: Test cleanup (delete the test image)
      console.log('\n5️⃣ Cleaning up test image...');
      const deleteResult = await StorageService.deleteImage(testPath);
      
      if (deleteResult.success) {
        console.log('✅ Test image deleted successfully!');
      } else {
        console.warn('⚠️ Failed to delete test image:', deleteResult.error);
      }
    } else {
      console.error('❌ Upload failed:', uploadResult.error);
    }

    console.log('\n🎉 Avatar upload test completed!');
    
  } catch (error) {
    console.error('💥 Test failed with error:', error);
  }
}

// Run the test
if (require.main === module) {
  testAvatarUpload().catch(console.error);
}

export { testAvatarUpload };
