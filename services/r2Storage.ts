import Constants from 'expo-constants';
import { compressForStorage } from '@/utils/imageCompression';
import { checkR2Configuration, printR2ConfigStatus } from '@/utils/r2ConfigCheck';
import { supabase } from '@/lib/supabase';

// R2 Configuration from environment variables
const R2_CONFIG = {
  ACCOUNT_ID: Constants.expoConfig?.extra?.cloudflareAccountId || process.env.CLOUDFLARE_ACCOUNT_ID,
  ACCESS_KEY_ID: Constants.expoConfig?.extra?.r2AccessKeyId || process.env.R2_ACCESS_KEY_ID,
  SECRET_ACCESS_KEY: Constants.expoConfig?.extra?.r2SecretAccessKey || process.env.R2_SECRET_ACCESS_KEY,
  BUCKET_NAME: Constants.expoConfig?.extra?.r2BucketName || process.env.R2_BUCKET_NAME,
  ENDPOINT_URL: Constants.expoConfig?.extra?.r2EndpointUrl || process.env.R2_ENDPOINT_URL,
};

// Validate R2 configuration
const validateR2Config = (): boolean => {
  const required = ['ACCOUNT_ID', 'ACCESS_KEY_ID', 'SECRET_ACCESS_KEY', 'BUCKET_NAME', 'ENDPOINT_URL'];
  const missing = required.filter(key => !R2_CONFIG[key as keyof typeof R2_CONFIG]);

  if (missing.length > 0) {
    console.warn('Missing R2 configuration:', missing);
    return false;
  }

  return true;
};

// R2 Client using direct HTTP requests (avoiding AWS SDK compatibility issues)
class R2Client {
  private config = R2_CONFIG;

  async uploadFile(
    key: string,
    file: Blob,
    contentType: string = 'application/octet-stream'
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      if (!validateR2Config()) {
        throw new Error('R2 configuration is incomplete');
      }

      console.log('Uploading to Cloudflare R2:', { key, size: file.size, type: contentType });

      // Use direct HTTP upload to R2 (simplified approach)
      // For now, we'll skip R2 and use Supabase as primary storage
      console.log('R2 upload temporarily disabled due to SDK compatibility issues');
      
      return {
        success: false,
        error: 'R2 upload temporarily disabled - using Supabase fallback',
      };
    } catch (error) {
      console.error('R2 upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  async deleteFile(key: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!validateR2Config()) {
        throw new Error('R2 configuration is incomplete');
      }

      console.log('Deleting from Cloudflare R2:', key);
      
      // R2 delete temporarily disabled due to SDK compatibility issues
      console.log('R2 delete temporarily disabled due to SDK compatibility issues');
      
      return {
        success: false,
        error: 'R2 delete temporarily disabled - using Supabase fallback',
      };
    } catch (error) {
      console.error('R2 delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }
}

// Storage service interface
export interface StorageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface StorageDeleteResult {
  success: boolean;
  error?: string;
}

// Main storage service that can fallback between R2 and Supabase
export class StorageService {
  private static r2Client = new R2Client();

  // Configuration option to disable storage uploads (useful for debugging)
  private static ENABLE_STORAGE_UPLOAD = true;

  static async uploadImage(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    console.log('🚀 StorageService.uploadImage called:', { path, size: file.size, type: file.type });

    // Check if storage uploads are disabled
    if (!this.ENABLE_STORAGE_UPLOAD) {
      console.log('⚠️ Storage uploads disabled, using data URL fallback');
      return this.uploadAsDataUrl(file, 5 * 1024 * 1024); // 5MB limit when disabled
    }

    // Skip R2 for now since it's disabled - go directly to Supabase
    console.log('⏭️ Skipping R2 (disabled), going directly to Supabase storage...');

    // Try Supabase storage
    console.log('📤 Attempting Supabase upload...');
    const supabaseResult = await this.uploadToSupabase(file, path, contentType);

    if (supabaseResult.success) {
      console.log('✅ Supabase upload successful!');
      return supabaseResult;
    }

    console.error('❌ Supabase upload failed, trying data URL fallback:', supabaseResult.error);

    // Final fallback to data URL for small images
    const dataUrlResult = await this.uploadAsDataUrl(file, 2 * 1024 * 1024); // 2MB limit for data URLs

    if (dataUrlResult.success) {
      console.log('✅ Successfully created data URL fallback');
      return dataUrlResult;
    }

    // If all methods fail, return the last error
    console.error('All upload methods failed');
    return {
      success: false,
      error: `All upload methods failed. Supabase: ${supabaseResult.error}, DataURL: ${dataUrlResult.error}`,
    };
  }

  /**
   * Upload image with automatic compression
   * This is the recommended method for uploading images from URIs
   */
  static async uploadImageFromUri(
    imageUri: string,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      console.log('Processing image for upload:', { imageUri, path });

      // Compress image for optimal storage
      const compressionResult = await compressForStorage(imageUri);
      console.log('Image compression result:', compressionResult);

      // Convert compressed image to blob
      const response = await fetch(compressionResult.uri);
      if (!response.ok) {
        throw new Error(`Failed to fetch compressed image: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      console.log('Compressed image blob:', { size: blob.size, type: blob.type });

      // Upload the compressed blob
      return this.uploadImage(blob, path, contentType || blob.type);
    } catch (error) {
      console.error('Image processing failed, trying direct upload:', error);

      // Fallback to direct upload without compression
      try {
        const response = await fetch(imageUri);
        if (!response.ok) {
          throw new Error(`Failed to fetch original image: ${response.status} ${response.statusText}`);
        }
        const blob = await response.blob();
        return this.uploadImage(blob, path, contentType || blob.type);
      } catch (fallbackError) {
        console.error('Direct upload also failed:', fallbackError);
        return {
          success: false,
          error: `Image upload failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`,
        };
      }
    }
  }

  // Test Supabase storage connectivity
  private static async testSupabaseStorage(): Promise<boolean> {
    try {
      console.log('🧪 Testing Supabase storage connectivity...');

      // Skip bucket listing since clients may not have permission to list buckets
      // Instead, just check if we have a valid session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        console.error('❌ Session error during storage test:', sessionError);
        return false;
      }

      if (!session) {
        console.error('❌ No active session for storage test');
        return false;
      }

      console.log('✅ Supabase storage connectivity test passed (session valid)');
      return true;
    } catch (error) {
      console.error('💥 Supabase storage test error:', error);
      return false;
    }
  }

  private static async uploadToSupabase(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      console.log('📤 Starting Supabase upload:', { path, size: file.size, type: file.type });

      // Test connectivity and authentication
      console.log('🔍 Testing Supabase storage connectivity...');
      const isConnected = await this.testSupabaseStorage();
      if (!isConnected) {
        console.error('❌ Supabase storage connectivity test failed');
        throw new Error('Supabase storage is not accessible');
      }
      console.log('✅ Supabase storage connectivity confirmed');

      console.log('📁 Uploading to bucket: user-content, path:', path);
      const { data, error } = await supabase.storage
        .from('user-content')
        .upload(path, file, {
          contentType: contentType || file.type,
          upsert: true
        });

      if (error) {
        console.error('❌ Supabase storage upload error:', error);
        console.error('Error details:', {
          message: error.message,
          statusCode: error.statusCode,
          error: error.error
        });
        throw error;
      }

      console.log('✅ Supabase upload successful:', data);

      // Get public URL
      console.log('🔗 Generating public URL...');
      const { data: { publicUrl } } = supabase.storage
        .from('user-content')
        .getPublicUrl(path);

      console.log('✅ Generated public URL:', publicUrl);

      return {
        success: true,
        url: publicUrl,
      };
    } catch (error) {
      console.error('💥 Supabase upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  // Fallback to data URL storage for small images
  private static async uploadAsDataUrl(
    file: Blob,
    maxSize: number = 1024 * 1024 // 1MB default
  ): Promise<StorageUploadResult> {
    try {
      if (file.size > maxSize) {
        return {
          success: false,
          error: `File too large for data URL storage (${file.size} bytes > ${maxSize} bytes)`,
        };
      }

      const reader = new FileReader();
      const dataUrl = await new Promise<string>((resolve, reject) => {
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      console.log('Created data URL fallback for image');

      return {
        success: true,
        url: dataUrl,
      };
    } catch (error) {
      console.error('Data URL creation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create data URL',
      };
    }
  }

  static async deleteImage(path: string): Promise<StorageDeleteResult> {
    // Try R2 first if configured
    if (validateR2Config()) {
      const result = await this.r2Client.deleteFile(path);
      if (result.success) {
        return result;
      }
    }

    // Fallback to Supabase storage
    try {
      const { error } = await supabase.storage
        .from('user-content')
        .remove([path]);

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Storage delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }

  // Helper method to generate unique file paths
  // Format: userId/type/timestamp-random.extension
  // This format ensures RLS policies work correctly (user ID in first folder)
  static generateImagePath(userId: string, type: 'avatar' | 'scan' | 'diagnosis', extension: string = 'jpg'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${userId}/${type}s/${timestamp}-${random}.${extension}`;
  }

  // Configuration methods
  static enableStorageUploads(enabled: boolean = true): void {
    this.ENABLE_STORAGE_UPLOAD = enabled;
    console.log(`Storage uploads ${enabled ? 'enabled' : 'disabled'}`);
  }

  static isStorageUploadEnabled(): boolean {
    return this.ENABLE_STORAGE_UPLOAD;
  }

  // Health check method
  static async checkStorageHealth(): Promise<{
    r2Available: boolean;
    supabaseAvailable: boolean;
    recommendedMethod: 'r2' | 'supabase' | 'dataurl';
  }> {
    const r2Available = validateR2Config();
    const supabaseAvailable = await this.testSupabaseStorage();

    let recommendedMethod: 'r2' | 'supabase' | 'dataurl' = 'dataurl';

    if (r2Available) {
      recommendedMethod = 'r2';
    } else if (supabaseAvailable) {
      recommendedMethod = 'supabase';
    }

    console.log('Storage health check:', {
      r2Available,
      supabaseAvailable,
      recommendedMethod,
    });

    return {
      r2Available,
      supabaseAvailable,
      recommendedMethod,
    };
  }

  // Configuration check methods
  static checkR2Config() {
    return checkR2Configuration();
  }

  static printR2ConfigStatus() {
    printR2ConfigStatus();
  }
}
